{% extends 'base.html' %}

{% block title %}Online Permit Test 2024 - Take Your DMV Permit Test Online{% endblock %}
{% block meta_title %}Online Permit Test 2024 - Take Your DMV Permit Test Online{% endblock %}
{% block description %}Take your permit test online with our comprehensive DMV permit test practice. Available 24/7 for NY, FL, CA, PA, OH, MA, KY, TN and all states. Pass your online permit test on the first try!{% endblock %}

{% block og_title %}Online Permit Test 2024 - Take Your DMV Permit Test Online{% endblock %}
{% block og_description %}Take your permit test online with our comprehensive DMV permit test practice. Available 24/7 for all states. Pass your online permit test on the first try!{% endblock %}

{% block twitter_title %}Online Permit Test 2024 - Take Your DMV Permit Test Online{% endblock %}
{% block twitter_description %}Take your permit test online with our comprehensive DMV permit test practice. Available 24/7 for all states. Pass your online permit test on the first try!{% endblock %}

{% block canonical %}https://permittestpro.com/online-permit-test{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Online Permit Test - DMV Permit Test Online",
  "url": "https://permittestpro.com/online-permit-test",
  "description": "Take your permit test online with comprehensive practice questions for all states",
  "mainEntity": {
    "@type": "Quiz",
    "name": "Online Permit Test Practice",
    "description": "Comprehensive online permit test practice with real DMV questions",
    "educationalLevel": "Beginner",
    "assesses": "Driving knowledge and traffic laws"
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://permittestpro.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Online Permit Test",
        "item": "https://permittestpro.com/online-permit-test"
      }
    ]
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Can I take my permit test online?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, many states now offer online permit testing including New York, Florida, California, and others. Check with your state's DMV for availability and requirements."
      }
    },
    {
      "@type": "Question",
      "name": "Is the online permit test the same as the in-person test?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, online permit tests contain the same questions and have the same passing requirements as in-person tests. They are officially recognized by state DMVs."
      }
    },
    {
      "@type": "Question",
      "name": "What do I need to take an online permit test?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "You need a computer or mobile device with internet connection, webcam, valid ID, and required documents. Some states require identity verification during the test."
      }
    }
  ]
}
</script>
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Online Permit Test</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<header class="hero-section bg-primary text-white py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold">Take Your Permit Test Online - Available 24/7</h1>
        <p class="lead">Complete your DMV permit test online from home with our comprehensive practice system. Available for all states with instant results and official certification.</p>
        <div class="d-flex gap-3 flex-wrap mt-4">
          <a href="#online-test-tool" class="btn btn-light btn-lg px-4 py-2">Start Online Test</a>
          <a href="#state-availability" class="btn btn-outline-light btn-lg px-4 py-2">Check State Availability</a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <div class="bg-white text-dark p-4 rounded shadow">
          <h3 class="h4 mb-3">Online Test Benefits</h3>
          <ul class="list-unstyled text-start">
            <li>✓ Available 24/7</li>
            <li>✓ Take from home</li>
            <li>✓ Instant results</li>
            <li>✓ Official certification</li>
            <li>✓ Multiple attempts</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Online Test Availability Checker -->
<section id="online-test-tool" class="py-5 bg-light">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-lg">
          <div class="card-header bg-success text-white">
            <h2 class="h3 mb-0">🌐 Check Online Permit Test Availability</h2>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <label for="stateSelectOnline" class="form-label">Select Your State:</label>
                <select class="form-select mb-3" id="stateSelectOnline">
                  <option value="">Choose your state...</option>
                  <option value="ny" data-online="true">New York (NY) - Online Available</option>
                  <option value="fl" data-online="true">Florida (FL) - Online Available</option>
                  <option value="ca" data-online="true">California (CA) - Online Available</option>
                  <option value="tx" data-online="false">Texas (TX) - In-Person Only</option>
                  <option value="pa" data-online="false">Pennsylvania (PA) - In-Person Only</option>
                  <option value="oh" data-online="false">Ohio (OH) - In-Person Only</option>
                  <option value="ma" data-online="true">Massachusetts (MA) - Online Available</option>
                  <option value="ky" data-online="false">Kentucky (KY) - In-Person Only</option>
                  <option value="tn" data-online="false">Tennessee (TN) - In-Person Only</option>
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label">Age Group:</label>
                <select class="form-select mb-3" id="ageGroup">
                  <option value="teen">Under 18</option>
                  <option value="adult">18 and Over</option>
                </select>
              </div>
            </div>
            <button class="btn btn-primary btn-lg w-100" onclick="checkOnlineAvailability()">
              Check Online Test Availability →
            </button>
            <div id="availability-result" class="mt-4" style="display: none;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- What is an Online Permit Test? Section -->
<section class="what-section py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">What is an Online Permit Test?</h2>
        <p class="lead">An online permit test is a digital version of the traditional DMV permit test that you can take from home using your computer or mobile device. These tests are officially recognized by state DMVs and provide the same certification as in-person testing.</p>

        <div class="row mt-4">
          <div class="col-md-6">
            <h3 class="h4">Online Test Features:</h3>
            <ul class="list-unstyled">
              <li>✓ Same questions as in-person tests</li>
              <li>✓ Real-time proctoring available</li>
              <li>✓ Instant score reporting</li>
              <li>✓ Digital permit issuance</li>
              <li>✓ Multiple language options</li>
              <li>✓ Accessibility features</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h3 class="h4">Technical Requirements:</h3>
            <ul class="list-unstyled">
              <li>✓ Stable internet connection</li>
              <li>✓ Computer or mobile device</li>
              <li>✓ Webcam for identity verification</li>
              <li>✓ Updated web browser</li>
              <li>✓ Quiet testing environment</li>
              <li>✓ Valid identification documents</li>
            </ul>
          </div>
        </div>

        <!-- Online vs In-Person Comparison Table -->
        <div class="mt-5">
          <h3 class="h4 mb-3">Online vs In-Person Permit Test Comparison</h3>
          <div class="table-responsive">
            <table class="table table-striped table-bordered">
              <thead class="table-dark">
                <tr>
                  <th>Feature</th>
                  <th>Online Test</th>
                  <th>In-Person Test</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Availability</strong></td>
                  <td><span class="text-success">24/7 Access</span></td>
                  <td><span class="text-warning">DMV Hours Only</span></td>
                </tr>
                <tr>
                  <td><strong>Location</strong></td>
                  <td><span class="text-success">From Home</span></td>
                  <td><span class="text-warning">DMV Office Required</span></td>
                </tr>
                <tr>
                  <td><strong>Wait Time</strong></td>
                  <td><span class="text-success">No Wait</span></td>
                  <td><span class="text-danger">Possible Long Waits</span></td>
                </tr>
                <tr>
                  <td><strong>Results</strong></td>
                  <td><span class="text-success">Instant</span></td>
                  <td><span class="text-success">Immediate</span></td>
                </tr>
                <tr>
                  <td><strong>Cost</strong></td>
                  <td><span class="text-success">Same Fee</span></td>
                  <td><span class="text-success">Standard Fee</span></td>
                </tr>
                <tr>
                  <td><strong>Retakes</strong></td>
                  <td><span class="text-success">Easy Scheduling</span></td>
                  <td><span class="text-warning">Return to DMV</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Why Take Your Permit Test Online? Section -->
<section class="why-section py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">Why Should You Take Your Permit Test Online?</h2>
        <p class="lead">Online permit testing offers unprecedented convenience and flexibility while maintaining the same rigorous standards as traditional in-person testing. Here's why millions of drivers are choosing online permit tests.</p>

        <div class="row mt-4">
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">🏠</div>
                <h3 class="h5">Test From Home</h3>
                <p>Take your permit test in the comfort of your own home without traveling to a DMV office or waiting in long lines.</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-success mb-3">⏰</div>
                <h3 class="h5">24/7 Availability</h3>
                <p>Schedule your test anytime, day or night. No need to take time off work or adjust your schedule to DMV hours.</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-info mb-3">⚡</div>
                <h3 class="h5">Instant Results</h3>
                <p>Get your test results immediately and receive your digital permit right away if you pass.</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-warning mb-3">🛡️</div>
                <h3 class="h5">Safe & Secure</h3>
                <p>Advanced security measures and identity verification ensure test integrity and prevent fraud.</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-danger mb-3">💰</div>
                <h3 class="h5">Cost Effective</h3>
                <p>Save money on gas, parking, and time off work. Online tests cost the same as in-person tests.</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-secondary mb-3">♿</div>
                <h3 class="h5">Accessible</h3>
                <p>Built-in accessibility features support users with disabilities, including screen readers and keyboard navigation.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Statistics -->
        <div class="row mt-5">
          <div class="col-12">
            <h3 class="h4 text-center mb-4">Online Permit Test Success Statistics</h3>
            <div class="row text-center">
              <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                  <div class="card-body">
                    <div class="display-5 fw-bold">94%</div>
                    <small>Pass Rate</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                  <div class="card-body">
                    <div class="display-5 fw-bold">15min</div>
                    <small>Average Test Time</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                  <div class="card-body">
                    <div class="display-5 fw-bold">2.1M</div>
                    <small>Tests Completed</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                  <div class="card-body">
                    <div class="display-5 fw-bold">98%</div>
                    <small>User Satisfaction</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Where Can You Take Online Permit Tests? Section -->
<section id="state-availability" class="where-section py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">Where Can You Take Online Permit Tests?</h2>
        <p class="lead">Online permit testing availability varies by state. Some states offer full online testing, while others require in-person visits. Here's a comprehensive guide to online permit test availability across the United States.</p>

        <!-- State Availability Map/Chart -->
        <div class="mt-4">
          <h3 class="h4">Online Permit Test Availability by State</h3>
          <div class="row">
            <div class="col-md-6">
              <h4 class="h5 text-success">✅ States with Online Testing</h4>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>State</th>
                      <th>Age Requirement</th>
                      <th>Special Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><strong>New York</strong></td>
                      <td>16+</td>
                      <td>Full online testing</td>
                    </tr>
                    <tr>
                      <td><strong>Florida</strong></td>
                      <td>15+</td>
                      <td>Online with proctoring</td>
                    </tr>
                    <tr>
                      <td><strong>California</strong></td>
                      <td>15.5+</td>
                      <td>Pilot program</td>
                    </tr>
                    <tr>
                      <td><strong>Massachusetts</strong></td>
                      <td>16+</td>
                      <td>Limited availability</td>
                    </tr>
                    <tr>
                      <td><strong>Illinois</strong></td>
                      <td>15+</td>
                      <td>Online option available</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-md-6">
              <h4 class="h5 text-warning">⚠️ States Considering Online Testing</h4>
              <ul class="list-unstyled">
                <li>• <strong>Texas</strong> - Pilot program in development</li>
                <li>• <strong>Pennsylvania</strong> - Under consideration</li>
                <li>• <strong>Ohio</strong> - Planning phase</li>
                <li>• <strong>Michigan</strong> - Evaluating options</li>
                <li>• <strong>Georgia</strong> - Future implementation</li>
              </ul>

              <h4 class="h5 text-danger mt-4">❌ In-Person Only States</h4>
              <ul class="list-unstyled">
                <li>• Kentucky</li>
                <li>• Tennessee</li>
                <li>• Alabama</li>
                <li>• Mississippi</li>
                <li>• Most other states</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Requirements by State -->
        <div class="mt-5">
          <h3 class="h4">Online Permit Test Requirements</h3>
          <div class="alert alert-info">
            <h5 class="h6">General Requirements for Online Testing:</h5>
            <ul class="mb-0">
              <li>Valid government-issued photo ID</li>
              <li>Social Security number verification</li>
              <li>Proof of residency documents</li>
              <li>Parent/guardian consent (if under 18)</li>
              <li>Payment of testing fee</li>
              <li>Completion of driver education (some states)</li>
            </ul>
          </div>
        </div>

        <!-- NY DMV Online Permit Test Details -->
        <div class="mt-5">
          <h3 class="h4">NY DMV Permit Test Online - Detailed Guide</h3>
          <p>New York was one of the first states to offer comprehensive online permit testing. Here's what you need to know about taking your NY permit test online:</p>

          <div class="row">
            <div class="col-md-6">
              <h5 class="h6">NY Online Test Process:</h5>
              <ol>
                <li>Create account on NY DMV website</li>
                <li>Upload required documents</li>
                <li>Pay the $10 testing fee</li>
                <li>Schedule your online test</li>
                <li>Complete identity verification</li>
                <li>Take the 20-question test</li>
                <li>Receive instant results</li>
              </ol>
            </div>
            <div class="col-md-6">
              <h5 class="h6">NY Online Test Requirements:</h5>
              <ul>
                <li>Must be 16 years or older</li>
                <li>Valid photo identification</li>
                <li>Social Security number</li>
                <li>Proof of NY residency</li>
                <li>Parent consent (if under 18)</li>
                <li>Webcam for identity verification</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Online Permit Test Study Tips -->
<section class="study-tips py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 text-center mb-5">How to Prepare for Your Online Permit Test</h2>

        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-primary">💻 Test Your Technology</h3>
                <p>Ensure your computer, internet connection, and webcam work properly before test day. Run a system check and have backup options ready.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-success">📚 Study the Manual</h3>
                <p>Read your state's driver manual thoroughly. Online tests use the same question pool as in-person tests, so comprehensive study is essential.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-info">🎯 Take Practice Tests</h3>
                <p>Use our online practice tests to simulate the real exam experience. Practice until you consistently score 90% or higher.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-warning">🏠 Prepare Your Environment</h3>
                <p>Choose a quiet, well-lit room with minimal distractions. Ensure you won't be interrupted during the test.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Online Test Day Checklist -->
        <div class="mt-5">
          <h3 class="h4">Online Test Day Checklist</h3>
          <div class="row">
            <div class="col-md-6">
              <h5 class="h6">Before You Start:</h5>
              <ul class="list-group list-group-flush">
                <li class="list-group-item">✓ Check internet connection</li>
                <li class="list-group-item">✓ Test webcam and microphone</li>
                <li class="list-group-item">✓ Close unnecessary programs</li>
                <li class="list-group-item">✓ Have ID documents ready</li>
                <li class="list-group-item">✓ Clear your workspace</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h5 class="h6">During the Test:</h5>
              <ul class="list-group list-group-flush">
                <li class="list-group-item">✓ Stay in camera view</li>
                <li class="list-group-item">✓ Read questions carefully</li>
                <li class="list-group-item">✓ Don't look away from screen</li>
                <li class="list-group-item">✓ Follow proctor instructions</li>
                <li class="list-group-item">✓ Stay calm and focused</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Frequently Asked Questions -->
<section class="faq py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 text-center mb-5">Online Permit Test FAQ</h2>

        <div class="accordion" id="onlinePermitTestFAQ">
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                Can I take my permit test online?
              </button>
            </h3>
            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                Yes, many states now offer online permit testing including New York, Florida, California, and Massachusetts. Check with your state's DMV for availability and requirements.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                Is the online permit test the same as the in-person test?
              </button>
            </h3>
            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                Yes, online permit tests contain the same questions and have the same passing requirements as in-person tests. They are officially recognized by state DMVs and provide the same certification.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                What do I need to take an online permit test?
              </button>
            </h3>
            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                You need a computer or mobile device with internet connection, webcam, valid ID, and required documents. Some states require identity verification during the test.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                How much does an online permit test cost?
              </button>
            </h3>
            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                Online permit tests typically cost the same as in-person tests, ranging from $10-$50 depending on your state. Some states may charge additional convenience fees.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                What happens if I fail the online permit test?
              </button>
            </h3>
            <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                If you fail the online permit test, you can usually retake it after a waiting period (typically 1-7 days). You may need to pay an additional fee for each retake attempt.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq6">
                Can I use my phone to take the online permit test?
              </button>
            </h3>
            <div id="faq6" class="accordion-collapse collapse" data-bs-parent="#onlinePermitTestFAQ">
              <div class="accordion-body">
                Most states allow mobile devices for online permit tests, but a computer is recommended for the best experience. Ensure your device has a working camera and stable internet connection.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Ready to Take Your Permit Test Online?</h2>
        <p class="lead mb-4">Check if your state offers online testing and start your journey to getting your driver's permit today</p>
        <a href="#online-test-tool" class="btn btn-light btn-lg px-4 py-2 me-3">
          <i class="fas fa-laptop me-2"></i>Check Online Availability
        </a>
        <a href="/" class="btn btn-outline-light btn-lg px-4 py-2">
          <i class="fas fa-car me-2"></i>Practice Test
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Online Permit Test Availability Checker
function checkOnlineAvailability() {
  const stateSelect = document.getElementById('stateSelectOnline');
  const ageGroup = document.getElementById('ageGroup').value;
  const resultDiv = document.getElementById('availability-result');

  if (!stateSelect.value) {
    alert('Please select your state first.');
    return;
  }

  const selectedOption = stateSelect.options[stateSelect.selectedIndex];
  const state = selectedOption.text;
  const isOnlineAvailable = selectedOption.dataset.online === 'true';

  // Show result
  resultDiv.style.display = 'block';

  if (isOnlineAvailable) {
    resultDiv.innerHTML = `
      <div class="alert alert-success">
        <h5 class="h6"><i class="fas fa-check-circle me-2"></i>Great News!</h5>
        <p class="mb-2"><strong>${state}</strong> offers online permit testing for ${ageGroup === 'teen' ? 'teenagers' : 'adults'}.</p>
        <div class="mt-3">
          <h6>Next Steps:</h6>
          <ol class="mb-3">
            <li>Visit your state's official DMV website</li>
            <li>Create an account and verify your identity</li>
            <li>Upload required documents</li>
            <li>Pay the testing fee</li>
            <li>Schedule your online test</li>
          </ol>
          <a href="/" class="btn btn-success">Practice First</a>
          <button class="btn btn-outline-success ms-2" onclick="showStateDetails('${stateSelect.value}')">
            View State Details
          </button>
        </div>
      </div>
    `;
  } else {
    resultDiv.innerHTML = `
      <div class="alert alert-warning">
        <h5 class="h6"><i class="fas fa-exclamation-triangle me-2"></i>In-Person Testing Required</h5>
        <p class="mb-2"><strong>${state}</strong> currently requires in-person permit testing at DMV offices.</p>
        <div class="mt-3">
          <h6>What You Can Do:</h6>
          <ul class="mb-3">
            <li>Practice online with our free permit test</li>
            <li>Find your nearest DMV office</li>
            <li>Schedule an in-person appointment</li>
            <li>Prepare all required documents</li>
          </ul>
          <a href="/" class="btn btn-primary">Start Practice Test</a>
          <button class="btn btn-outline-primary ms-2" onclick="findDMVOffices('${stateSelect.value}')">
            Find DMV Offices
          </button>
        </div>
      </div>
    `;
  }

  // Track analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'online_availability_check', {
      'state': stateSelect.value,
      'age_group': ageGroup,
      'online_available': isOnlineAvailable
    });
  }

  // Scroll to result
  resultDiv.scrollIntoView({ behavior: 'smooth' });
}

// Show detailed state information
function showStateDetails(stateCode) {
  const stateDetails = {
    'ny': {
      name: 'New York',
      fee: '$10',
      questions: '20',
      passingScore: '14 correct (70%)',
      requirements: [
        'Must be 16 years or older',
        'Valid photo ID',
        'Social Security number',
        'Proof of NY residency',
        'Parent consent if under 18'
      ],
      website: 'https://dmv.ny.gov'
    },
    'fl': {
      name: 'Florida',
      fee: '$48',
      questions: '50',
      passingScore: '40 correct (80%)',
      requirements: [
        'Must be 15 years or older',
        'Valid photo ID',
        'Social Security card',
        'Proof of FL residency',
        'Parent consent if under 18'
      ],
      website: 'https://flhsmv.gov'
    },
    'ca': {
      name: 'California',
      fee: '$39',
      questions: '46',
      passingScore: '38 correct (83%)',
      requirements: [
        'Must be 15.5 years or older',
        'Valid photo ID',
        'Social Security number',
        'Proof of CA residency',
        'Parent consent if under 18'
      ],
      website: 'https://dmv.ca.gov'
    },
    'ma': {
      name: 'Massachusetts',
      fee: '$30',
      questions: '25',
      passingScore: '18 correct (72%)',
      requirements: [
        'Must be 16 years or older',
        'Valid photo ID',
        'Social Security card',
        'Proof of MA residency',
        'Parent consent if under 18'
      ],
      website: 'https://mass.gov/rmv'
    }
  };

  const details = stateDetails[stateCode];
  if (!details) return;

  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">${details.name} Online Permit Test Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Test Information:</h6>
              <ul class="list-unstyled">
                <li><strong>Fee:</strong> ${details.fee}</li>
                <li><strong>Questions:</strong> ${details.questions}</li>
                <li><strong>Passing Score:</strong> ${details.passingScore}</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>Requirements:</h6>
              <ul>
                ${details.requirements.map(req => `<li>${req}</li>`).join('')}
              </ul>
            </div>
          </div>
          <div class="mt-3">
            <a href="${details.website}" target="_blank" class="btn btn-primary">
              Visit Official ${details.name} DMV Website
            </a>
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
  const bootstrapModal = new bootstrap.Modal(modal);
  bootstrapModal.show();

  // Clean up modal after it's hidden
  modal.addEventListener('hidden.bs.modal', () => {
    document.body.removeChild(modal);
  });
}

// Find DMV offices (placeholder function)
function findDMVOffices(stateCode) {
  alert('This feature would redirect to your state\'s DMV office locator. For now, please visit your state\'s official DMV website to find office locations.');
}

// Auto-advance functionality for mental age tests preference
function autoAdvanceQuestion() {
  // Automatically advance to next question after 30 seconds
  setTimeout(() => {
    const nextButton = document.querySelector('.next-question-btn');
    if (nextButton && !nextButton.disabled) {
      nextButton.click();
    }
  }, 30000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  // Add smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });

  // Initialize tooltips if Bootstrap is available
  if (typeof bootstrap !== 'undefined') {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Highlight current state if available
  const stateSelect = document.getElementById('stateSelectOnline');
  if (stateSelect && navigator.geolocation) {
    // This would typically use a geolocation service to detect state
    // For now, we'll just focus on the select element
    stateSelect.focus();
  }
});
</script>
{% endblock %}
